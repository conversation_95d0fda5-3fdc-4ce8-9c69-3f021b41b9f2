<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>MCP 搜索工具选型与接入指南</title>
  <style>
    :root{
      --fg:#1f2328;           /* 前景文字（浅色） */
      --bg:#ffffff;           /* 页面背景（浅色） */
      --muted:#57606a;        /* 次要文字 */
      --code-bg:#f6f8fa;      /* 代码块背景（浅色） */
      --border:#d0d7de;       /* 边框线条 */
      --link:#0969da;         /* 链接颜色（浅色） */
      --toc-bg:#fafbfc;       /* 目录背景（浅色） */
    }
    [data-theme="dark"]{
      --fg:#e6edf3;           /* 前景文字（深蓝） */
      --bg:#0b1221;           /* 页面背景（深蓝） */
      --muted:#9fb0c3;        /* 次要文字（深蓝） */
      --code-bg:#0f1b31;      /* 代码块背景（深蓝） */
      --border:#223152;       /* 边框线条（深蓝） */
      --link:#8ab4f8;         /* 链接颜色（深蓝） */
      --toc-bg:#0d1830;       /* 目录背景（深蓝） */
    }

    html,body{margin:0;padding:0;background:var(--bg);color:var(--fg);font:16px/1.6 -apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica,Arial,"Noto Sans",sans-serif,"Apple Color Emoji","Segoe UI Emoji"}
    .page{max-width:960px;margin:0 auto;padding:28px 20px 60px}
    h1{font-size:28px;margin:0 0 8px}
    h2{font-size:22px;margin:28px 0 10px;padding-top:8px;border-top:1px solid var(--border)}
    h3{font-size:18px;margin:18px 0 8px}
    p{margin:10px 0}
    ul,ol{margin:8px 0 12px 22px}
    code{background:var(--code-bg);border:1px solid var(--border);border-radius:4px;padding:0 4px;font-family:ui-monospace,SFMono-Regular,Consolas,"Liberation Mono",Menlo,monospace;font-size:0.95em}
    pre{background:var(--code-bg);border:1px solid var(--border);border-radius:6px;padding:12px;overflow:auto}
    pre code{border:none;padding:0;background:transparent}
    .muted{color:var(--muted)}
    a{color:var(--link);text-decoration:none}
    .toc{background:var(--toc-bg);border:1px solid var(--border);border-radius:6px;padding:12px;margin:16px 0}
    .toc ul{margin:0 0 0 18px}
    .footer{margin-top:36px;color:var(--muted);font-size:13px}

    .theme-toggle{position:fixed;top:12px;right:16px;background:var(--code-bg);color:var(--fg);border:1px solid var(--border);border-radius:8px;padding:6px 10px;font-size:14px;cursor:pointer}
    .theme-toggle:hover{filter:brightness(1.05)}
    .theme-toggle:focus{outline:2px solid var(--link);outline-offset:2px}
    ::selection{background:#1e3a8a;color:#e5e7eb}
  </style>
</head>
<body>
  <div class="page">
    <h1>MCP 搜索工具选型与接入指南</h1>
    <div class="muted">更新时间：2025-08-20</div>
    <button id="theme-toggle" class="theme-toggle" aria-label="切换主题">切换为深蓝</button>

    <nav class="toc">
      <strong>目录</strong>
      <ul>
        <li><a href="#sec1">1. MCP 与“搜索工具”概念</a></li>
        <li><a href="#sec2">2. 推荐搜索源（可作为 MCP 工具后端）</a>
          <ul>
            <li><a href="#sec2-1">2.1 Tavily</a></li>
            <li><a href="#sec2-2">2.2 Bing Web Search（Azure）</a></li>
            <li><a href="#sec2-3">2.3 SerpAPI</a></li>
            <li><a href="#sec2-4">2.4 Brave Search API</a></li>
            <li><a href="#sec2-5">2.5 SearxNG 自建</a></li>
            <li><a href="#sec2-6">2.6 国内引擎官方/企业 API</a></li>
          </ul>
        </li>
        <li><a href="#sec3">3. 统一的 MCP 工具签名（建议）</a></li>
        <li><a href="#sec4">4. MCP 适配实现要点</a></li>
        <li><a href="#sec5">5. 参考实现（伪代码/接口示意）</a></li>
        <li><a href="#sec6">6. 选型建议（速览）</a></li>
        <li><a href="#sec7">7. 安全与合规</a></li>
        <li><a href="#sec8">8. 后续可扩展</a></li>
      </ul>
    </nav>

    <h2 id="sec1">1. MCP 与“搜索工具”概念</h2>
    <p>Model Context Protocol（MCP）是一种为智能体/助手暴露外部功能的工具协议。将“网页搜索”能力封装为 MCP 工具后，助手可以以统一的接口调用不同搜索源（例如 Bing、Tavily、SerpAPI、SearxNG 等），并实现路由、降级与聚合。</p>
    <p>典型做法：定义一个通用的 <code>search.query</code> 工具签名，底层接任意搜索提供方，通过配置切换或多源兜底。</p>

    <h2 id="sec2">2. 推荐搜索源（可作为 MCP 工具后端）</h2>

    <h3 id="sec2-1">2.1 Tavily（推荐开箱即用）</h3>
    <ul>
      <li>优点：接口简单、返回结构化摘要，适合 Agent 工作流与检索增强。</li>
      <li>缺点：境外服务，中文可用度尚可但非最优；需要 API Key。</li>
      <li>适用：需要快速落地、统一数据结构与摘要能力。</li>
    </ul>
    <p>示例（curl）：</p>
    <pre><code>curl -X POST https://api.tavily.com/search \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TAVILY_API_KEY" \
  -d '{"query":"ltshwy.gtimg.com 是什么","search_depth":"advanced","max_results":5}'
</code></pre>

    <h3 id="sec2-2">2.2 Bing Web Search（Azure Cognitive Services）</h3>
    <ul>
      <li>优点：稳定、中文覆盖较好、合规渠道清晰。</li>
      <li>缺点：需要 Azure 订阅与配额；返回格式偏通用，需要自行抽取摘要。</li>
      <li>适用：中文/综合搜索质量与稳定性优先的场景。</li>
    </ul>
    <p>示例（curl）：</p>
    <pre><code>curl -G "https://api.bing.microsoft.com/v7.0/search" \
  -H "Ocp-Apim-Subscription-Key: $BING_KEY" \
  --data-urlencode "q=ltshwy.gtimg.com 是什么" \
  --data-urlencode "count=5" --data-urlencode "mkt=zh-CN"
</code></pre>

    <h3 id="sec2-3">2.3 SerpAPI（多引擎聚合）</h3>
    <ul>
      <li>优点：一套 API 封装多引擎（Google/Bing/Brave…）；切换引擎成本低。</li>
      <li>缺点：付费、速率限制；不同引擎质量差异需调参。</li>
      <li>适用：希望在一个 MCP 工具里“随时切源/多源兜底”。</li>
    </ul>
    <p>示例（curl，Bing 引擎）：</p>
    <pre><code>curl -G "https://serpapi.com/search.json" \
  --data-urlencode "engine=bing" \
  --data-urlencode "q=ltshwy.gtimg.com 是什么" \
  --data-urlencode "num=5" \
  --data-urlencode "api_key=$SERPAPI_KEY"
</code></pre>

    <h3 id="sec2-4">2.4 Brave Search API（隐私友好）</h3>
    <ul>
      <li>优点：注重隐私；英文技术内容质量较好。</li>
      <li>缺点：中文覆盖一般；需 Key。</li>
      <li>适用：隐私优先或英文主场景。</li>
    </ul>

    <h3 id="sec2-5">2.5 SearxNG 自建（聚合与自控）</h3>
    <ul>
      <li>优点：自托管聚合搜索，可路由国内外多源；隐私与可控性强。</li>
      <li>缺点：需要维护；部分数据源可能需要代理/Cookie。</li>
      <li>适用：需要国内可达、可自定义源、合规和可观测性。</li>
    </ul>
    <p>快速部署（示意）：</p>
    <pre><code># docker-compose.yaml 略；部署后开放 /search 接口
curl -G "https://your-searxng.example.com/search" \
  --data-urlencode "q=ltshwy.gtimg.com 是什么" \
  --data-urlencode "format=json" \
  --data-urlencode "language=zh-CN"
</code></pre>

    <h3 id="sec2-6">2.6 国内引擎官方/企业 API（可选）</h3>
    <ul>
      <li>说明：百度/360/神马等通用网页搜索 API 多为合作或专项接口，公众渠道有限。</li>
      <li>建议：若有企业合作接口，可直接包一层 MCP；否则以 Bing 或 SearxNG 聚合为主。</li>
    </ul>

    <h2 id="sec3">3. 统一的 MCP 工具签名（建议）</h2>
    <p>为方便替换后端，推荐统一定义：</p>
    <ul>
      <li>工具名：<code>search.query</code></li>
      <li>入参 JSON：
        <ul>
          <li><code>q</code>（string）：查询语句</li>
          <li><code>top_k</code>（number，默认 5）：返回条数</li>
          <li><code>site</code>（string，可选）：站点限定，如 <code>site:docs.python.org</code></li>
          <li><code>time_range</code>（string，可选）：<code>past_day|past_week|past_month|past_year|any</code></li>
        </ul>
      </li>
      <li>返回 JSON：
        <ul>
          <li><code>items</code>：数组，每项包含
            <ul>
              <li><code>title</code>（string）</li>
              <li><code>url</code>（string）</li>
              <li><code>snippet</code>（string）</li>
              <li><code>source</code>（string，例如 <code>bing</code>/<code>tavily</code>/<code>serpapi:google</code>/<code>searxng</code>）</li>
              <li><code>score</code>（number，可选，自定义排序分数）</li>
            </ul>
          </li>
          <li><code>meta</code>：
            <ul>
              <li><code>took_ms</code>（number）</li>
              <li><code>from_cache</code>（boolean）</li>
            </ul>
          </li>
        </ul>
      </li>
    </ul>
    <p>这样即可在不改上层调用的前提下替换/并行多个搜索源。</p>

    <h2 id="sec4">4. MCP 适配实现要点</h2>
    <ol>
      <li>路由与降级：主源失败→备源；按地域/语言选择最优源。</li>
      <li>标准化：将不同源的返回规范化为统一的 <code>items</code> 结构；清洗空标题、重复链接、过短片段。</li>
      <li>摘要与去重：必要时对前几条结果抓取首屏 HTML，抽取 &lt;title&gt; 与 meta description，或用轻量规则生成摘要；统一去重（URL 归一化）。</li>
      <li>限流与重试：结合 429/5xx 策略，启用指数退避与并发上限；记录配额。</li>
      <li>观测与审计：记录请求 ID、源、耗时、命中率、错误类型；日志脱敏。</li>
      <li>配置化：通过环境变量切换主备源与密钥；支持灰度、开关与站点白/黑名单。</li>
    </ol>

    <h2 id="sec5">5. 参考实现（伪代码/接口示意）</h2>

    <h3>5.1 Tavily 适配（伪代码）</h3>
    <pre><code>function queryTavily(q, top_k, time_range){
  const body = {query: q, search_depth: 'advanced', max_results: top_k};
  if(time_range &amp;&amp; time_range !== 'any'){ body.time_range = time_range; }
  const resp = http.post('https://api.tavily.com/search', body, {
    headers: { Authorization: `Bearer ${TAVILY_API_KEY}` }
  });
  return resp.results.map(r => ({
    title: r.title || '', url: r.url, snippet: r.content || r.snippet || '', source: 'tavily'
  }));
}
</code></pre>

    <h3>5.2 Bing 适配（伪代码）</h3>
    <pre><code>function queryBing(q, top_k){
  const url = `https://api.bing.microsoft.com/v7.0/search?q=${enc(q)}&amp;count=${top_k}&amp;mkt=zh-CN`;
  const resp = http.get(url, { headers: { 'Ocp-Apim-Subscription-Key': BING_KEY }});
  const webPages = (resp.webPages &amp;&amp; resp.webPages.value) || [];
  return webPages.map(r => ({
    title: r.name || '', url: r.url, snippet: r.snippet || '', source: 'bing'
  }));
}
</code></pre>

    <h3>5.3 SearxNG 适配（伪代码）</h3>
    <pre><code>function querySearxng(q, top_k){
  const url = `https://your-searxng/search?q=${enc(q)}&amp;format=json&amp;language=zh-CN&amp;categories=general`;
  const resp = http.get(url);
  return (resp.results || []).slice(0, top_k).map(r => ({
    title: r.title || '', url: r.url, snippet: r.content || '', source: 'searxng'
  }));
}
</code></pre>

    <h3>5.4 统一入口（伪代码）</h3>
    <pre><code>function searchQuery({q, top_k=5, site, time_range='any'}){
  if(site){ q = `${q} site:${site}`; }
  try { return { items: queryTavily(q, top_k, time_range), meta: { source: 'tavily' } }; }
  catch(e1){ try { return { items: queryBing(q, top_k), meta: { source: 'bing' } }; }
  catch(e2){ return { items: querySearxng(q, top_k), meta: { source: 'searxng' } }; }
}
</code></pre>

    <h2 id="sec6">6. 选型建议（速览）</h2>
    <ul>
      <li>快速上线与结构化结果：优先 Tavily；</li>
      <li>稳定中文综合质量与合规：优先 Bing；</li>
      <li>多引擎切换与兜底：SerpAPI；</li>
      <li>隐私与自控、可接国内源：SearxNG 自建；</li>
      <li>若有国内引擎企业接口：直接封装为 MCP 后端并做源路由。</li>
    </ul>

    <h2 id="sec7">7. 安全与合规</h2>
    <ul>
      <li>严禁将私有密钥写入代码库；使用环境变量与密钥管理。</li>
      <li>尊重目标站点 robots 与使用条款；避免过度抓取。</li>
      <li>对外部结果进行基本内容安全检查与来源标注。</li>
    </ul>

    <h2 id="sec8">8. 后续可扩展</h2>
    <ul>
      <li>增加新闻/学术/代码搜索专用源（GDELT、Semantic Scholar、Sourcegraph 等）。</li>
      <li>为中文优化分词与重排（BM25/关键短语提升），提升中文检索质量。</li>
      <li>引入缓存（键：query+time_range+site），减少配额与时延。</li>
    </ul>

    <hr />
    <p>如需，我可以在本仓库内继续补充：</p>
    <ul>
      <li>实际可运行的 MCP 工具适配（Node/Python 二选一）；</li>
      <li>Docker 化的 SearxNG 一键部署示例；</li>
      <li>针对你的环境的密钥与路由配置样例（不包含密钥值）。</li>
    </ul>

    <div class="footer">© 当前文档为静态 HTML 渲染版本，方便在浏览器中直观查看。</div>
  </div>
  <script>
    (function(){
      var storageKey = 'mcp-theme';
      function preferred(){
        try{
          var saved = localStorage.getItem(storageKey);
          if(saved === 'light' || saved === 'dark') return saved;
        }catch(e){}
        return (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) ? 'dark' : 'light';
      }
      function apply(theme){
        document.documentElement.setAttribute('data-theme', theme);
        try{ localStorage.setItem(storageKey, theme); }catch(e){}
        var btn = document.getElementById('theme-toggle');
        if(btn){ btn.textContent = theme === 'dark' ? '切换为浅色' : '切换为深蓝'; btn.setAttribute('aria-pressed', theme === 'dark'); }
      }
      document.addEventListener('DOMContentLoaded', function(){
        apply(preferred());
        var btn = document.getElementById('theme-toggle');
        if(btn){
          btn.addEventListener('click', function(){
            var current = document.documentElement.getAttribute('data-theme') || 'light';
            apply(current === 'dark' ? 'light' : 'dark');
          });
        }
      });
    })();
  </script>
</body>
</html>

