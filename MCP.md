# MCP 搜索工具选型与接入指南

更新时间：2025-08-20

## 1. MCP 与“搜索工具”概念
Model Context Protocol（MCP）是一种为智能体/助手暴露外部功能的工具协议。将“网页搜索”能力封装为 MCP 工具后，助手可以以统一的接口调用不同搜索源（例如 Bing、Tavily、SerpAPI、SearxNG 等），并实现路由、降级与聚合。

典型做法：定义一个通用的 `search.query` 工具签名，底层接任意搜索提供方，通过配置切换或多源兜底。

## 2. 推荐搜索源（可作为 MCP 工具后端）

### 2.1 Tavily（推荐开箱即用）
- 优点：接口简单、返回结构化摘要，适合 Agent 工作流与检索增强。
- 缺点：境外服务，中文可用度尚可但非最优；需要 API Key。
- 适用：需要快速落地、统一数据结构与摘要能力。

示例（curl）：
```
curl -X POST https://api.tavily.com/search \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TAVILY_API_KEY" \
  -d '{"query":"ltshwy.gtimg.com 是什么","search_depth":"advanced","max_results":5}'
```

### 2.2 Bing Web Search（Azure Cognitive Services）
- 优点：稳定、中文覆盖较好、合规渠道清晰。
- 缺点：需要 Azure 订阅与配额；返回格式偏通用，需要自行抽取摘要。
- 适用：中文/综合搜索质量与稳定性优先的场景。

示例（curl）：
```
curl -G "https://api.bing.microsoft.com/v7.0/search" \
  -H "Ocp-Apim-Subscription-Key: $BING_KEY" \
  --data-urlencode "q=ltshwy.gtimg.com 是什么" \
  --data-urlencode "count=5" --data-urlencode "mkt=zh-CN"
```

### 2.3 SerpAPI（多引擎聚合）
- 优点：一套 API 封装多引擎（Google/Bing/Brave…）；切换引擎成本低。
- 缺点：付费、速率限制；不同引擎质量差异需调参。
- 适用：希望在一个 MCP 工具里“随时切源/多源兜底”。

示例（curl，Bing 引擎）：
```
curl -G "https://serpapi.com/search.json" \
  --data-urlencode "engine=bing" \
  --data-urlencode "q=ltshwy.gtimg.com 是什么" \
  --data-urlencode "num=5" \
  --data-urlencode "api_key=$SERPAPI_KEY"
```

### 2.4 Brave Search API（隐私友好）
- 优点：注重隐私；英文技术内容质量较好。
- 缺点：中文覆盖一般；需 Key。
- 适用：隐私优先或英文主场景。

### 2.5 SearxNG 自建（聚合与自控）
- 优点：自托管聚合搜索，可路由国内外多源；隐私与可控性强。
- 缺点：需要维护；部分数据源可能需要代理/Cookie。
- 适用：需要国内可达、可自定义源、合规和可观测性。

快速部署（示意）：
```
# docker-compose.yaml 略；部署后开放 /search 接口
curl -G "https://your-searxng.example.com/search" \
  --data-urlencode "q=ltshwy.gtimg.com 是什么" \
  --data-urlencode "format=json" \
  --data-urlencode "language=zh-CN"
```

### 2.6 国内引擎官方/企业 API（可选）
- 说明：百度/360/神马等通用网页搜索 API 多为合作或专项接口，公众渠道有限。
- 建议：若有企业合作接口，可直接包一层 MCP；否则以 Bing 或 SearxNG 聚合为主。

## 3. 统一的 MCP 工具签名（建议）
为方便替换后端，推荐统一定义：

- 工具名：`search.query`
- 入参 JSON：
  - `q`（string）：查询语句
  - `top_k`（number，默认 5）：返回条数
  - `site`（string，可选）：站点限定，如 `site:docs.python.org`
  - `time_range`（string，可选）：`past_day|past_week|past_month|past_year|any`
- 返回 JSON：
  - `items`：数组，每项包含
    - `title`（string）
    - `url`（string）
    - `snippet`（string）
    - `source`（string，例如 `bing`/`tavily`/`serpapi:google`/`searxng`）
    - `score`（number，可选，自定义排序分数）
  - `meta`：
    - `took_ms`（number）
    - `from_cache`（boolean）

这样即可在不改上层调用的前提下替换/并行多个搜索源。

## 4. MCP 适配实现要点
1) 路由与降级：主源失败→备源；按地域/语言选择最优源。
2) 标准化：将不同源的返回规范化为统一的 `items` 结构；清洗空标题、重复链接、过短片段。
3) 摘要与去重：必要时对前几条结果抓取首屏 HTML，抽取 <title> 与 meta description，或用轻量规则生成摘要；统一去重（URL 归一化）。
4) 限流与重试：结合 429/5xx 策略，启用指数退避与并发上限；记录配额。
5) 观测与审计：记录请求 ID、源、耗时、命中率、错误类型；日志脱敏。
6) 配置化：通过环境变量切换主备源与密钥；支持灰度、开关与站点白/黑名单。

## 5. 参考实现（伪代码/接口示意）

### 5.1 Tavily 适配（伪代码）
```
function queryTavily(q, top_k, time_range){
  const body = {query: q, search_depth: 'advanced', max_results: top_k};
  if(time_range && time_range !== 'any'){ body.time_range = time_range; }
  const resp = http.post('https://api.tavily.com/search', body, {
    headers: { Authorization: `Bearer ${TAVILY_API_KEY}` }
  });
  return resp.results.map(r => ({
    title: r.title || '', url: r.url, snippet: r.content || r.snippet || '', source: 'tavily'
  }));
}
```

### 5.2 Bing 适配（伪代码）
```
function queryBing(q, top_k){
  const url = `https://api.bing.microsoft.com/v7.0/search?q=${enc(q)}&count=${top_k}&mkt=zh-CN`;
  const resp = http.get(url, { headers: { 'Ocp-Apim-Subscription-Key': BING_KEY }});
  const webPages = (resp.webPages && resp.webPages.value) || [];
  return webPages.map(r => ({
    title: r.name || '', url: r.url, snippet: r.snippet || '', source: 'bing'
  }));
}
```

### 5.3 SearxNG 适配（伪代码）
```
function querySearxng(q, top_k){
  const url = `https://your-searxng/search?q=${enc(q)}&format=json&language=zh-CN&categories=general`;
  const resp = http.get(url);
  return (resp.results || []).slice(0, top_k).map(r => ({
    title: r.title || '', url: r.url, snippet: r.content || '', source: 'searxng'
  }));
}
```

### 5.4 统一入口（伪代码）
```
function searchQuery({q, top_k=5, site, time_range='any'}){
  if(site){ q = `${q} site:${site}`; }
  try { return { items: queryTavily(q, top_k, time_range), meta: { source: 'tavily' } }; }
  catch(e1){ try { return { items: queryBing(q, top_k), meta: { source: 'bing' } }; }
  catch(e2){ return { items: querySearxng(q, top_k), meta: { source: 'searxng' } }; }
}
```

## 6. 选型建议（速览）
- 快速上线与结构化结果：优先 Tavily；
- 稳定中文综合质量与合规：优先 Bing；
- 多引擎切换与兜底：SerpAPI；
- 隐私与自控、可接国内源：SearxNG 自建；
- 若有国内引擎企业接口：直接封装为 MCP 后端并做源路由。

## 7. 安全与合规
- 严禁将私有密钥写入代码库；使用环境变量与密钥管理。
- 尊重目标站点 robots 与使用条款；避免过度抓取。
- 对外部结果进行基本内容安全检查与来源标注。

## 8. 后续可扩展
- 增加新闻/学术/代码搜索专用源（GDELT、Semantic Scholar、Sourcegraph 等）。
- 为中文优化分词与重排（BM25/关键短语提升），提升中文检索质量。
- 引入缓存（键：query+time_range+site），减少配额与时延。

---
如需，我可以在本仓库内继续补充：
- 实际可运行的 MCP 工具适配（Node/Python 二选一）；
- Docker 化的 SearxNG 一键部署示例；
- 针对你的环境的密钥与路由配置样例（不包含密钥值）。

